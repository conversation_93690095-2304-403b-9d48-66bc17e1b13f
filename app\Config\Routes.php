<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');
$routes->get('home', 'Home::index');
$routes->get('home/login', 'Home::login');
$routes->post('home/authenticate', 'Home::authenticate');
$routes->get('dashboard', 'DashboardController::index');

// Dakoii System Routes
$routes->get('dakoii', 'Dakoii::index');
$routes->get('dakoii/dashboard', 'Dakoii::dashboard');
$routes->post('dakoii/authenticate', 'Dakoii::authenticate');
$routes->get('dakoii/organizations', 'Dakoii::organizations');
$routes->get('dakoii/organizations/create', 'Dakoii::createOrganization');
$routes->get('dakoii/admins', 'Dakoii::admins');
$routes->get('dakoii/admins/create', 'Dakoii::createAdmin');
$routes->get('dakoii/settings', 'Dakoii::settings');
$routes->get('dakoii/reports', 'Dakoii::reports');

// Administrator Settings Routes
$routes->group('admin', function($routes) {
    // Main admin dashboard
    $routes->get('/', 'AdminController::index');

    // Users Management Routes
    $routes->get('users', 'AdminController::users');
    $routes->get('users/create', 'AdminController::createUser');
    $routes->post('users', 'AdminController::storeUser');
    $routes->get('users/(:num)/edit', 'AdminController::editUser/$1');
    $routes->put('users/(:num)', 'AdminController::updateUser/$1');
    $routes->delete('users/(:num)', 'AdminController::deleteUser/$1');

    // Structure Management Routes - Using AdminStructureController
    $routes->get('structures', 'AdminStructureController::index');
    $routes->get('structures/create', 'AdminStructureController::create');
    $routes->post('structures', 'AdminStructureController::store');
    $routes->get('structures/(:num)/edit', 'AdminStructureController::edit/$1');
    $routes->put('structures/(:num)', 'AdminStructureController::update/$1');
    $routes->post('structures/(:num)/activate', 'AdminStructureController::activate/$1');
    $routes->delete('structures/(:num)', 'AdminStructureController::delete/$1');

    // Group Management Routes
    $routes->get('structures/(:num)/groups', 'AdminStructureController::groups/$1');
    $routes->get('structures/(:num)/groups/create', 'AdminStructureController::createGroup/$1');
    $routes->post('structures/(:num)/groups', 'AdminStructureController::storeGroup/$1');
    $routes->get('structures/(:num)/groups/(:num)/edit', 'AdminStructureController::editGroup/$1/$2');
    $routes->put('structures/(:num)/groups/(:num)', 'AdminStructureController::updateGroup/$1/$2');
    $routes->delete('structures/(:num)/groups/(:num)', 'AdminStructureController::deleteGroup/$1/$2');

    // Position Management Routes
    $routes->get('groups/(:num)/positions', 'AdminStructureController::positions/$1');
    $routes->get('groups/(:num)/positions/create', 'AdminStructureController::createPosition/$1');
    $routes->post('groups/(:num)/positions', 'AdminStructureController::storePosition/$1');
    $routes->get('groups/(:num)/positions/(:num)/edit', 'AdminStructureController::editPosition/$1/$2');
    $routes->put('groups/(:num)/positions/(:num)', 'AdminStructureController::updatePosition/$1/$2');
    $routes->delete('groups/(:num)/positions/(:num)', 'AdminStructureController::deletePosition/$1/$2');

    // Appointments Routes
    $routes->get('appointments', 'AdminController::appointments');
    $routes->get('appointments/create', 'AdminController::createAppointment');
    $routes->post('appointments', 'AdminController::storeAppointment');

    // Plans Management Routes
    $routes->get('plans', 'AdminController::plans');

    // Budget Book Management Routes
    $routes->get('budget-books', 'AdminController::budgetBooks');

    // Legislated Activities Routes
    $routes->get('legislated-activities', 'AdminController::legislatedActivities');
    $routes->get('legislation', 'AdminController::legislation');
    $routes->get('legislation/create', 'AdminController::createLegislation');
    $routes->post('legislation', 'AdminController::storeLegislation');
    $routes->get('legislation/(:num)/activities', 'AdminController::activities/$1');
    $routes->get('legislation/(:num)/activities/create', 'AdminController::createActivity/$1');
    $routes->post('legislation/(:num)/activities', 'AdminController::storeActivity/$1');
});
