<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: #1a1a1a;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        border: 1px solid #333;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table-dark th {
        background-color: #2d2d2d;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .kpi-target {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        color: #ffc107;
    }

    .kpi-current {
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }

    .performance-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }

    .performance-exceeding { background-color: #28a745; }
    .performance-on-track { background-color: #17a2b8; }
    .performance-needs-attention { background-color: #ffc107; }
    .performance-critical { background-color: #dc3545; }

    .frequency-badge {
        font-size: 0.65rem;
        padding: 0.15rem 0.3rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-chart-line me-2"></i>Key Performance Indicators (KPIs)
                        </h3>
                        <p class="text-muted mb-1">
                            Plan: <strong class="text-warning"><?= esc($plan['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0">
                            KRA: <strong class="text-info"><?= esc($kra['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0 small">
                            Manage Key Performance Indicators within this KRA.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/kras') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to KRAs
                            </a>
                            <a href="<?= site_url('admin/kras/' . $kra['id'] . '/kpis/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create KPI
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KRA Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row">
                    <div class="col-md-3">
                        <small class="text-muted">KRA Weight</small>
                        <div><span class="badge bg-warning text-dark"><?= $kra['weight'] ?>%</span></div>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">Description</small>
                        <div><small class="text-light"><?= esc($kra['description']) ?></small></div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">KPIs</small>
                        <div><span class="badge bg-info"><?= count($kpis) ?></span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- KPIs List -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-tachometer-alt me-2"></i>Performance Indicators
                    </h5>
                    <span class="badge bg-info">
                        <?= count($kpis) ?> KPI<?= count($kpis) !== 1 ? 's' : '' ?>
                    </span>
                </div>

                <?php if (empty($kpis)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No KPIs Found</h5>
                        <p class="text-muted">Create your first Key Performance Indicator within this KRA.</p>
                        <a href="<?= site_url('admin/kras/' . $kra['id'] . '/kpis/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Create First KPI
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>KPI Name</th>
                                    <th>Target</th>
                                    <th>Current Value</th>
                                    <th>Performance</th>
                                    <th>Frequency</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($kpis as $kpi): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong class="text-light"><?= esc($kpi['name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= esc($kpi['description']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="kpi-target"><?= esc($kpi['target']) ?></div>
                                        <small class="text-muted"><?= esc($kpi['measurement']) ?></small>
                                    </td>
                                    <td>
                                        <div class="kpi-current text-<?= 
                                            $kpi['status'] === 'Exceeding' ? 'success' : 
                                            ($kpi['status'] === 'On Track' ? 'info' : 
                                            ($kpi['status'] === 'Needs Attention' ? 'warning' : 'danger')) 
                                        ?>">
                                            <?= esc($kpi['current_value']) ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="performance-indicator performance-<?= 
                                                strtolower(str_replace(' ', '-', $kpi['status'])) 
                                            ?>"></span>
                                            <span class="badge bg-<?= 
                                                $kpi['status'] === 'Exceeding' ? 'success' : 
                                                ($kpi['status'] === 'On Track' ? 'info' : 
                                                ($kpi['status'] === 'Needs Attention' ? 'warning' : 'danger')) 
                                            ?> status-badge">
                                                <?= esc($kpi['status']) ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary frequency-badge">
                                            <?= esc($kpi['frequency']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= site_url('admin/kras/' . $kra['id'] . '/kpis/' . $kpi['id'] . '/edit') ?>" 
                                               class="btn btn-outline-primary btn-action" title="Edit KPI">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?= site_url('admin/kras/' . $kra['id'] . '/kpis/' . $kpi['id']) ?>" class="d-inline">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-outline-danger btn-action" 
                                                        title="Delete KPI"
                                                        onclick="return confirm('Are you sure you want to delete this KPI? This action cannot be undone.')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Performance Summary -->
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-12">
                                <small class="text-muted">Performance Overview:</small>
                                <div class="d-flex gap-3 mt-2">
                                    <?php
                                    $statusCounts = array_count_values(array_column($kpis, 'status'));
                                    $statusColors = [
                                        'Exceeding' => 'success',
                                        'On Track' => 'info', 
                                        'Needs Attention' => 'warning',
                                        'Critical' => 'danger'
                                    ];
                                    ?>
                                    <?php foreach ($statusColors as $status => $color): ?>
                                        <div class="text-center">
                                            <div class="badge bg-<?= $color ?> mb-1">
                                                <?= $statusCounts[$status] ?? 0 ?>
                                            </div>
                                            <div><small class="text-muted"><?= $status ?></small></div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1 text-primary">
                            <i class="fas fa-info-circle me-2"></i>Navigation Flow
                        </h6>
                        <p class="mb-0 text-muted small">
                            Plans → KRAs → <strong>KPIs</strong>. You've reached the final level of the corporate planning hierarchy.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/kras') ?>" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>Back to KRAs
                            </a>
                            <a href="<?= site_url('admin/kras/' . $kra['id'] . '/kpis/create') ?>" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-2"></i>Add KPI
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        console.log('KPIs Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
