<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: #1a1a1a;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        border: 1px solid #333;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table-dark th {
        background-color: #2d2d2d;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .weight-display {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        color: #28a745;
    }

    .kra-weight-bar {
        background-color: #333;
        border-radius: 4px;
        height: 8px;
        overflow: hidden;
        position: relative;
    }

    .kra-weight-fill {
        height: 100%;
        background: linear-gradient(90deg, #800000, #28a745);
        transition: width 0.3s ease;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-bullseye me-2"></i>Key Result Areas (KRAs)
                        </h3>
                        <p class="text-muted mb-1">
                            Plan: <strong class="text-warning"><?= esc($plan['name']) ?></strong>
                        </p>
                        <p class="text-muted mb-0 small">
                            Manage Key Result Areas within this corporate plan.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/plans') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Plans
                            </a>
                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/kras/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create KRA
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plan Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row">
                    <div class="col-md-3">
                        <small class="text-muted">Plan Type</small>
                        <div><span class="badge bg-warning text-dark"><?= esc($plan['type']) ?></span></div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">Fiscal Year</small>
                        <div><span class="badge bg-secondary"><?= $plan['fiscal_year'] ?></span></div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">Status</small>
                        <div><span class="badge bg-<?= $plan['status'] === 'Active' ? 'success' : 'secondary' ?>"><?= esc($plan['status']) ?></span></div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">KRAs</small>
                        <div><span class="badge bg-info"><?= count($kras) ?></span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- KRAs List -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-target me-2"></i>Key Result Areas
                    </h5>
                    <span class="badge bg-info">
                        <?= count($kras) ?> KRA<?= count($kras) !== 1 ? 's' : '' ?>
                    </span>
                </div>

                <?php if (empty($kras)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-bullseye fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No KRAs Found</h5>
                        <p class="text-muted">Create your first Key Result Area within this corporate plan.</p>
                        <a href="<?= site_url('admin/plans/' . $plan['id'] . '/kras/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Create First KRA
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>KRA Name</th>
                                    <th>Weight (%)</th>
                                    <th>Status</th>
                                    <th>KPIs</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($kras as $kra): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong class="text-light"><?= esc($kra['name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= esc($kra['description']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="weight-display me-2"><?= $kra['weight'] ?>%</span>
                                            <div class="kra-weight-bar flex-grow-1" style="width: 60px;">
                                                <div class="kra-weight-fill" style="width: <?= $kra['weight'] ?>%;"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $kra['status'] === 'Active' ? 'success' : 'secondary' ?> status-badge">
                                            <?= esc($kra['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= site_url('admin/kras/' . $kra['id'] . '/kpis') ?>" 
                                           class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-chart-line me-1"></i>View KPIs
                                        </a>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('M d, Y', strtotime($kra['created_at'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/kras/' . $kra['id'] . '/edit') ?>" 
                                               class="btn btn-outline-primary btn-action" title="Edit KRA">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="<?= site_url('admin/plans/' . $plan['id'] . '/kras/' . $kra['id']) ?>" class="d-inline">
                                                <?= csrf_field() ?>
                                                <input type="hidden" name="_method" value="DELETE">
                                                <button type="submit" class="btn btn-outline-danger btn-action" 
                                                        title="Delete KRA"
                                                        onclick="return confirm('Are you sure you want to delete this KRA? This will also delete all associated KPIs.')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Weight Summary -->
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">Total Weight Distribution:</small>
                                <div class="progress mt-1" style="height: 10px;">
                                    <?php 
                                    $totalWeight = array_sum(array_column($kras, 'weight'));
                                    $progressColor = $totalWeight == 100 ? 'bg-success' : ($totalWeight > 100 ? 'bg-danger' : 'bg-warning');
                                    ?>
                                    <div class="progress-bar <?= $progressColor ?>" style="width: <?= min($totalWeight, 100) ?>%;">
                                        <?= $totalWeight ?>%
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <?php if ($totalWeight != 100): ?>
                                    <small class="text-<?= $totalWeight > 100 ? 'danger' : 'warning' ?>">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Weight should total 100% (Currently: <?= $totalWeight ?>%)
                                    </small>
                                <?php else: ?>
                                    <small class="text-success">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Weight distribution is balanced
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Navigation Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1 text-primary">
                            <i class="fas fa-info-circle me-2"></i>Navigation Flow
                        </h6>
                        <p class="mb-0 text-muted small">
                            Plans → <strong>KRAs</strong> → KPIs. Click "View KPIs" to manage Key Performance Indicators within each KRA.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/plans') ?>" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>Back to Plans
                            </a>
                            <a href="<?= site_url('admin/plans/' . $plan['id'] . '/kras/create') ?>" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-2"></i>Add KRA
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        console.log('KRAs Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
